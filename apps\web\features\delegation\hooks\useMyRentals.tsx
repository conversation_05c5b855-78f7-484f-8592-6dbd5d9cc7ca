"use client";

import { useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  IRentalWithMetadata,
  IMyRentalsResponse,
  ERentalStatus,
  IRental,
} from "../types/delegation.types";
import { DelegationAPI } from "../api/delegation.api";
import useChickenMetadata from "@/features/breeding/tab/breeding/hooks/useChickenMetadata";
import { IChickenMetadata } from "@/lib/types/chicken.types";

// Real API functions
const fetchMyRentals = async (): Promise<IMyRentalsResponse> => {
  return await DelegationAPI.getMyRentals();
};

const cancelRental = async (
  rentalId: number
): Promise<{ status: number; message: string }> => {
  return await DelegationAPI.cancelRental(rentalId);
};

// Helper function to create rental with real metadata
const createRentalWithRealMetadata = (
  rental: IRental,
  metadataMap: Record<number, IChickenMetadata>
): IRentalWithMetadata => {
  const metadata = metadataMap[rental.chickenTokenId];

  // Extract daily feathers and legendary count from metadata attributes
  const dailyFeathersAttr = metadata?.attributes.find(
    (attr) => attr.trait_type === "Daily Feathers"
  );
  const legendaryCountAttr = metadata?.attributes.find(
    (attr) => attr.trait_type === "Legendary Count"
  );

  const dailyFeathers = dailyFeathersAttr?.value
    ? typeof dailyFeathersAttr.value === "string"
      ? parseInt(dailyFeathersAttr.value)
      : Number(dailyFeathersAttr.value)
    : 0;

  const legendaryCount = legendaryCountAttr?.value
    ? typeof legendaryCountAttr.value === "string"
      ? parseInt(legendaryCountAttr.value)
      : Number(legendaryCountAttr.value)
    : 0;

  return {
    ...rental,
    chickenMetadata: metadata,
    dailyFeathers,
    legendaryCount,
  };
};

export function useMyRentals(currentUserAddress?: string) {
  const queryClient = useQueryClient();

  // Fetch user's rentals
  const {
    data: myRentalsResponse,
    isLoading: isLoadingRentals,
    error,
    refetch,
  } = useQuery({
    queryKey: ["my-rentals", currentUserAddress],
    queryFn: fetchMyRentals,
    enabled: !!currentUserAddress,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });

  // Extract token IDs from rental data for metadata fetching
  const tokenIds = useMemo(() => {
    if (!myRentalsResponse?.data) return [];

    const allRentals = [
      ...myRentalsResponse.data.ownedRentals,
      ...myRentalsResponse.data.rentedChickens,
    ];

    return allRentals.map((rental) => rental.chickenTokenId);
  }, [myRentalsResponse?.data]);

  // Fetch chicken metadata for all rentals
  const {
    metadataMap,
    isLoading: isLoadingMetadata,
    error: metadataError,
  } = useChickenMetadata(tokenIds);

  // Convert rentals to include real metadata
  const allRentalsWithMetadata: IRentalWithMetadata[] = useMemo(() => {
    if (!myRentalsResponse?.data) return [];

    const allRentals = [
      ...myRentalsResponse.data.ownedRentals,
      ...myRentalsResponse.data.rentedChickens,
    ];

    return allRentals.map((rental) =>
      createRentalWithRealMetadata(rental, metadataMap)
    );
  }, [myRentalsResponse?.data, metadataMap]);

  // Separate owned vs rented chickens using API response structure
  const { ownedRentals, rentedChickens } = useMemo(() => {
    if (!myRentalsResponse?.data) {
      return { ownedRentals: [], rentedChickens: [] };
    }

    const owned = myRentalsResponse.data.ownedRentals.map((rental) =>
      createRentalWithRealMetadata(rental, metadataMap)
    );

    const rented = myRentalsResponse.data.rentedChickens.map((rental) =>
      createRentalWithRealMetadata(rental, metadataMap)
    );

    return { ownedRentals: owned, rentedChickens: rented };
  }, [myRentalsResponse?.data, metadataMap]);

  // Further categorize owned rentals
  const ownedRentalsByStatus = useMemo(() => {
    return {
      active: ownedRentals.filter((r) => r.status === ERentalStatus.RENTED),
      available: ownedRentals.filter(
        (r) => r.status === ERentalStatus.AVAILABLE
      ),
      expired: ownedRentals.filter((r) => r.status === ERentalStatus.EXPIRED),
      cancelled: ownedRentals.filter(
        (r) => r.status === ERentalStatus.CANCELLED
      ),
    };
  }, [ownedRentals]);

  // Categorize rented chickens
  const rentedChickensByStatus = useMemo(() => {
    return {
      active: rentedChickens.filter((r) => r.status === ERentalStatus.RENTED),
      expired: rentedChickens.filter((r) => r.status === ERentalStatus.EXPIRED),
    };
  }, [rentedChickens]);

  // Cancel rental mutation
  const cancelMutation = useMutation({
    mutationFn: cancelRental,
    onSuccess: () => {
      toast.success("Rental cancelled successfully!");

      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["my-rentals"] });
      queryClient.invalidateQueries({
        queryKey: ["my-rentals", currentUserAddress],
      });
      queryClient.invalidateQueries({ queryKey: ["available-rentals"] });
      queryClient.invalidateQueries({ queryKey: ["chickens"] });
      queryClient.invalidateQueries({ queryKey: ["chickenRentalStatuses"] });
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to cancel rental";
      toast.error(errorMessage);
    },
  });

  // Handle cancel rental
  const handleCancelRental = async (rentalId: number) => {
    try {
      await cancelMutation.mutateAsync(rentalId);
      return true;
    } catch {
      return false;
    }
  };

  // Calculate statistics
  const stats = useMemo(() => {
    const totalEarnings = ownedRentalsByStatus.active.reduce((sum, rental) => {
      return sum + parseFloat(rental.roninPrice) / 1e18;
    }, 0);

    const totalSpent = rentedChickensByStatus.active.reduce((sum, rental) => {
      return sum + parseFloat(rental.roninPrice) / 1e18;
    }, 0);

    return {
      totalOwnedRentals: ownedRentals.length,
      activeOwnedRentals: ownedRentalsByStatus.active.length,
      availableListings: ownedRentalsByStatus.available.length,
      totalRentedChickens: rentedChickens.length,
      activeRentedChickens: rentedChickensByStatus.active.length,
      totalEarnings,
      totalSpent,
      netProfit: totalEarnings - totalSpent,
    };
  }, [
    ownedRentals,
    rentedChickens,
    ownedRentalsByStatus,
    rentedChickensByStatus,
  ]);

  // Combined loading state
  const isLoading = isLoadingRentals || isLoadingMetadata;

  return {
    // Data
    allRentals: allRentalsWithMetadata,
    ownedRentals,
    rentedChickens,
    ownedRentalsByStatus,
    rentedChickensByStatus,
    stats,

    // Loading states
    isLoading,
    isCancelling: cancelMutation.isPending,

    // Error states
    error: error || metadataError,
    cancelError: cancelMutation.error,

    // Actions
    refetch,
    cancelRental: handleCancelRental,

    // Utilities
    reset: () => {
      cancelMutation.reset();
    },
  };
}
