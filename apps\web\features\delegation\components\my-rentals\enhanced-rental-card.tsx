"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "ui";
import { cn } from "@/utils/classes";
import Image from "next/image";
import {
  Clock,
  User,
  Zap,
  Info,
  AlertTriangle,
  CheckCircle,
} from "lucide-react";
import { Feathers } from "@/components/shared/icons/feathers";
import {
  IRentalWithMetadata,
  formatRentalDuration,
  formatRoninPrice,
  REWARD_DISTRIBUTION_LABELS,
  DELEGATED_TASK_LABELS,
  ERentalStatus,
  getRentalRemainingSeconds,
  calculateMarketplaceFee,
  calculateEarningsAfterFees,
  formatMarketplaceFeePercentage,
} from "../../types/delegation.types";
import { useMarketplaceFee } from "../../hooks/useMarketplaceFee";
import { useUnlistChickenForRent } from "../../hooks/useUnlistChickenForRent";
import { useClaimInsurance } from "../../hooks/useClaimInsurance";
import { useChickenDeathVerification } from "../../hooks/useChickenDeathVerification";
import { RentalStatusBadge } from "../shared/rental-status-badge";
import { useStateContext } from "@/providers/app/state";
import { CooldownTimer } from "@/components/common/cooldown-timer";
import { ChickenDetailsModal } from "../rental-marketplace/chicken-details-modal";

interface IEnhancedRentalCardProps {
  rental: IRentalWithMetadata;
  onCancel?: (rental: IRentalWithMetadata) => void;
  isCancelling?: boolean;
  className?: string;
  showInsuranceActions?: boolean;
}

export function EnhancedRentalCard({
  rental,
  onCancel,
  isCancelling = false,
  className,
  showInsuranceActions = false,
}: IEnhancedRentalCardProps) {
  const { address } = useStateContext();
  const { feePercentage } = useMarketplaceFee();
  const { executeUnlistChickenForRent, isUnlisting } =
    useUnlistChickenForRent();
  const { executeClaimInsurance, checkInsuranceEligibility, isClaiming } =
    useClaimInsurance();
  const { useChickenDeathQuery } = useChickenDeathVerification();

  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showInsuranceDetails, setShowInsuranceDetails] = useState(false);

  // Check if current user is the owner
  const isOwner = address?.toLowerCase() === rental.ownerAddress.toLowerCase();
  const isRenter =
    address?.toLowerCase() === rental.renterAddress?.toLowerCase();

  // Get chicken death verification for insurance eligibility
  const { data: deathVerification } = useChickenDeathQuery(
    rental.chickenTokenId,
    !!showInsuranceActions &&
      (rental.status === ERentalStatus.EXPIRED ||
        (rental.expiresAt && new Date(rental.expiresAt) < new Date()))
  );

  // Calculate earnings after marketplace fees
  const dailyRate = (() => {
    if (rental.roninPrice === "0") return 0;
    const totalPriceRon = parseFloat(formatRoninPrice(rental.roninPrice));
    const durationDays = Math.floor(rental.rentalPeriod / 86400);
    return durationDays > 0 ? totalPriceRon / durationDays : totalPriceRon;
  })();
  const marketplaceFee = calculateMarketplaceFee(dailyRate, feePercentage);
  const earningsAfterFees = calculateEarningsAfterFees(
    dailyRate,
    feePercentage
  );

  // Determine address display info
  const addressInfo = (() => {
    if (isOwner) {
      return {
        label: rental.renterAddress ? "Renter" : "Available",
        displayText: rental.renterAddress
          ? `${rental.renterAddress.slice(0, 6)}...${rental.renterAddress.slice(-4)}`
          : "No renter yet",
      };
    } else {
      return {
        label: "Owner",
        displayText: `${rental.ownerAddress.slice(0, 6)}...${rental.ownerAddress.slice(-4)}`,
      };
    }
  })();

  // Handle unlisting
  const handleUnlist = async () => {
    try {
      const result = await executeUnlistChickenForRent(rental.id);
      if (result?.success) {
        // Success message is handled by the hook
      }
    } catch (error) {
      console.error("Failed to unlist chicken:", error);
    }
  };

  // Handle insurance claim
  const handleClaimInsurance = async () => {
    try {
      const result = await executeClaimInsurance(rental);
      if (result?.success) {
        // Success message is handled by the hook
      }
    } catch (error) {
      console.error("Failed to claim insurance:", error);
    }
  };

  // Check insurance eligibility
  const checkInsurance = async () => {
    try {
      const eligibility = await checkInsuranceEligibility(rental);
      console.log("Insurance eligibility:", eligibility);
      setShowInsuranceDetails(true);
    } catch (error) {
      console.error("Failed to check insurance eligibility:", error);
    }
  };

  // Determine if rental has expired
  const isExpired = rental.expiresAt && new Date(rental.expiresAt) < new Date();
  const canUnlist = isOwner && rental.status === ERentalStatus.AVAILABLE;
  const canClaimInsurance =
    showInsuranceActions && isExpired && (isOwner || isRenter);

  return (
    <div
      className={cn(
        "bg-stone-800/50 backdrop-blur-sm border border-stone-700/50 rounded-xl p-4 hover:border-stone-600/50 transition-all duration-200 relative",
        className
      )}
    >
      {/* Status Badge */}
      <div className="absolute top-3 right-3">
        <RentalStatusBadge status={rental.status} />
      </div>

      {/* Chicken Image and Basic Info */}
      <div className="flex items-start gap-3 mb-4">
        <div className="relative">
          <Image
            src={
              rental.chickenMetadata?.image || "/images/chicken-placeholder.png"
            }
            alt={`Chicken #${rental.chickenTokenId}`}
            width={64}
            height={64}
            className="rounded-lg border border-stone-600/50"
          />
          <button
            onClick={() => setShowDetailsModal(true)}
            className="absolute -top-1 -right-1 bg-blue-500 hover:bg-blue-600 text-white rounded-full p-1 transition-colors"
          >
            <Info className="w-3 h-3" />
          </button>
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h3 className="text-white font-semibold text-sm truncate">
              {rental.chickenMetadata?.name ||
                `Chicken #${rental.chickenTokenId}`}
            </h3>
          </div>

          <div className="text-gray-400 text-xs mb-2">
            Token ID: #{rental.chickenTokenId}
          </div>

          {/* Price Display */}
          <div className="flex items-center gap-2">
            <div className="text-yellow-400 font-bold text-sm">
              {(() => {
                if (rental.roninPrice === "0") return "Free";
                const totalPriceRon = parseFloat(
                  formatRoninPrice(rental.roninPrice)
                );
                const durationDays = Math.floor(rental.rentalPeriod / 86400);
                const dailyRate =
                  durationDays > 0
                    ? totalPriceRon / durationDays
                    : totalPriceRon;
                return `${dailyRate.toFixed(4)} RON/day`;
              })()}
            </div>
            {feePercentage > 0 && isOwner && (
              <Tooltip delay={0}>
                <Tooltip.Trigger>
                  <div className="text-green-400 text-xs cursor-help">
                    ≈{earningsAfterFees.toFixed(4)} RON/day
                  </div>
                </Tooltip.Trigger>
                <Tooltip.Content>
                  <div className="text-sm">
                    Marketplace fee:{" "}
                    {formatMarketplaceFeePercentage(feePercentage)} (
                    {marketplaceFee.toFixed(4)} RON/day)
                  </div>
                </Tooltip.Content>
              </Tooltip>
            )}
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="flex gap-2 mb-3">
        <div className="flex-1 bg-stone-700/30 rounded-lg p-2 text-center">
          <Clock className="w-4 h-4 text-blue-400 mx-auto mb-1" />
          <div className="text-gray-400 text-xs">
            {rental.status === ERentalStatus.RENTED ? "Time Left" : "Duration"}
          </div>
          <div className="text-white font-semibold text-xs">
            {rental.status === ERentalStatus.RENTED && rental.expiresAt ? (
              getRentalRemainingSeconds(rental) > 0 ? (
                <CooldownTimer
                  remainingSeconds={getRentalRemainingSeconds(rental)}
                  className="text-white font-semibold text-xs"
                />
              ) : (
                <span className="text-red-400">Expired</span>
              )
            ) : (
              formatRentalDuration(rental.rentalPeriod)
            )}
          </div>
        </div>

        {rental.dailyFeathers && rental.dailyFeathers > 0 && (
          <div className="flex-1 bg-stone-700/30 rounded-lg p-2 text-center">
            <Feathers size={16} className="text-yellow-400 mx-auto mb-1" />
            <div className="text-gray-400 text-xs">Daily Feathers</div>
            <div className="text-yellow-400 font-bold text-xs">
              {rental.dailyFeathers}
            </div>
          </div>
        )}

        <div className="flex-1 bg-stone-700/30 rounded-lg p-2 text-center">
          <User className="w-4 h-4 text-purple-400 mx-auto mb-1" />
          <div className="text-gray-400 text-xs">{addressInfo.label}</div>
          <div className="text-white font-mono text-xs">
            {addressInfo.displayText}
          </div>
        </div>
      </div>

      {/* Delegation Terms */}
      <div className="mb-4">
        <div className="text-xs text-gray-400 mb-2">Delegation Terms</div>
        <div className="flex flex-wrap gap-2">
          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-300 border border-blue-500/30">
            {DELEGATED_TASK_LABELS[rental.delegatedTask]}
          </span>
          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-purple-500/20 text-purple-300 border border-purple-500/30">
            {REWARD_DISTRIBUTION_LABELS[rental.rewardDistribution]}
            {rental.rewardDistribution === 3 && rental.sharedRewardAmount && (
              <span className="ml-1 text-yellow-400 font-bold">
                ({rental.sharedRewardAmount} 🪶/day)
              </span>
            )}
          </span>
        </div>
      </div>

      {/* Insurance Status */}
      {showInsuranceActions && isExpired && (
        <div className="mb-4 p-3 bg-stone-700/30 rounded-lg border border-stone-600/50">
          <div className="flex items-center gap-2 mb-2">
            {deathVerification?.isDead ? (
              <AlertTriangle className="w-4 h-4 text-red-400" />
            ) : (
              <CheckCircle className="w-4 h-4 text-green-400" />
            )}
            <span className="text-xs font-medium text-gray-300">
              Insurance Status
            </span>
          </div>
          <div className="text-xs text-gray-400">
            {deathVerification?.isDead
              ? "Chicken died during rental - Owner eligible for insurance"
              : "Chicken survived rental - Renter eligible for refund"}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-2">
        {canUnlist && (
          <Button
            className="flex-1 bg-red-600/20 hover:bg-red-600/30 text-red-400 border border-red-600/50 hover:border-red-500"
            size="small"
            appearance="outline"
            onPress={handleUnlist}
            isDisabled={isUnlisting}
          >
            {isUnlisting ? "Unlisting..." : "Unlist"}
          </Button>
        )}

        {onCancel && rental.status === ERentalStatus.AVAILABLE && (
          <Button
            className="flex-1 bg-orange-600/20 hover:bg-orange-600/30 text-orange-400 border border-orange-600/50 hover:border-orange-500"
            size="small"
            appearance="outline"
            onPress={() => onCancel(rental)}
            isDisabled={isCancelling}
          >
            {isCancelling ? "Cancelling..." : "Cancel"}
          </Button>
        )}

        {canClaimInsurance && (
          <Button
            className="flex-1 bg-green-600/20 hover:bg-green-600/30 text-green-400 border border-green-600/50 hover:border-green-500"
            size="small"
            appearance="outline"
            onPress={handleClaimInsurance}
            isDisabled={isClaiming}
          >
            {isClaiming ? "Claiming..." : "Claim Insurance"}
          </Button>
        )}
      </div>

      {/* Chicken Details Modal */}
      <ChickenDetailsModal
        isOpen={showDetailsModal}
        onOpenChange={setShowDetailsModal}
        rental={rental}
      />
    </div>
  );
}
