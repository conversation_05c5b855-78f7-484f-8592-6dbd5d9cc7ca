"use client";

import { useState, useEffect } from "react";
import { Button, Dialog, Input, Select, Textarea } from "ui";
import { Calculator, Clock, Coins, Info, Shield } from "lucide-react";
import { useCreateRental } from "../../hooks/useCreateRental";
import {
  ICreateRentalFormData,
  ERewardDistributionType,
  EDelegatedTaskType,
  REWARD_DISTRIBUTION_LABELS,
  DELEGATED_TASK_LABELS,
} from "../../types/delegation.types";
import { IChickenMetadata } from "@/lib/types/chicken.types";

interface IRentalListingDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  chickenTokenId?: number;
  chickenMetadata?: IChickenMetadata;
  onSuccess?: () => void;
}

export function RentalListingDialog({
  isOpen,
  onOpenChange,
  chickenTokenId,
  chickenMetadata,
  onSuccess,
}: IRentalListingDialogProps) {
  const { executeCreateRental, validateFormData, isCreating } = useCreateRental();

  const [formData, setFormData] = useState<ICreateRentalFormData>({
    chickenTokenId: chickenTokenId || 0,
    roninPrice: "",
    rentalPeriod: 86400, // 1 day default
    rewardDistribution: ERewardDistributionType.DELEGATOR_ONLY,
    delegatedTask: EDelegatedTaskType.BOTH,
    isDirectDelegation: false,
    sharedRewardAmount: undefined,
    renterAddress: "",
  });

  const [insurancePrice, setInsurancePrice] = useState("");
  const [errors, setErrors] = useState<string[]>([]);

  // Update chicken token ID when prop changes
  useEffect(() => {
    if (chickenTokenId) {
      setFormData(prev => ({ ...prev, chickenTokenId }));
    }
  }, [chickenTokenId]);

  // Duration options
  const durationOptions = [
    { value: 3600, label: "1 Hour" },
    { value: 86400, label: "1 Day" },
    { value: 259200, label: "3 Days" },
    { value: 604800, label: "1 Week" },
    { value: 1209600, label: "2 Weeks" },
    { value: 2592000, label: "30 Days" },
    { value: 7776000, label: "90 Days" },
  ];

  // Reward distribution options
  const rewardDistributionOptions = Object.entries(REWARD_DISTRIBUTION_LABELS).map(
    ([value, label]) => ({
      value: parseInt(value),
      label,
    })
  );

  // Delegated task options
  const delegatedTaskOptions = Object.entries(DELEGATED_TASK_LABELS).map(
    ([value, label]) => ({
      value: parseInt(value),
      label,
    })
  );

  // Calculate total rental price and insurance
  const dailyRate = parseFloat(formData.roninPrice) || 0;
  const durationInDays = formData.rentalPeriod / 86400;
  const totalRentalPrice = dailyRate * durationInDays;
  const insuranceAmount = parseFloat(insurancePrice) || totalRentalPrice * 0.5; // Default 50%

  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    const validationErrors = validateFormData(formData);
    
    // Additional validation for insurance
    if (insurancePrice && parseFloat(insurancePrice) < 0) {
      validationErrors.push("Insurance price cannot be negative");
    }

    setErrors(validationErrors);

    if (validationErrors.length > 0) {
      return;
    }

    try {
      const result = await executeCreateRental(formData);
      if (result?.success) {
        onOpenChange(false);
        onSuccess?.();
        // Reset form
        setFormData({
          chickenTokenId: 0,
          roninPrice: "",
          rentalPeriod: 86400,
          rewardDistribution: ERewardDistributionType.DELEGATOR_ONLY,
          delegatedTask: EDelegatedTaskType.BOTH,
          isDirectDelegation: false,
          sharedRewardAmount: undefined,
          renterAddress: "",
        });
        setInsurancePrice("");
      }
    } catch (error) {
      console.error("Failed to create rental:", error);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
    setErrors([]);
  };

  return (
    <Dialog isOpen={isOpen} onOpenChange={handleClose} size="lg">
      <Dialog.Content>
        <Dialog.Header>
          <Dialog.Title>List Chicken for Rent</Dialog.Title>
          <Dialog.Description>
            Set rental terms and list your chicken in the marketplace
          </Dialog.Description>
        </Dialog.Header>

        <div className="space-y-6 max-h-96 overflow-y-auto">
          {/* Chicken Information */}
          {chickenMetadata && (
            <div className="bg-stone-700/30 rounded-lg p-4">
              <h3 className="text-sm font-medium text-white mb-2">Chicken Details</h3>
              <div className="flex items-center gap-3">
                <img
                  src={chickenMetadata.image}
                  alt={chickenMetadata.name}
                  className="w-12 h-12 rounded-lg border border-stone-600"
                />
                <div>
                  <div className="text-white font-medium">{chickenMetadata.name}</div>
                  <div className="text-gray-400 text-sm">Token ID: #{chickenTokenId}</div>
                </div>
              </div>
            </div>
          )}

          {/* Pricing */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-white flex items-center gap-2">
              <Coins className="w-4 h-4" />
              Pricing
            </h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm text-gray-400 mb-2">
                  Daily Rate (RON)
                </label>
                <Input
                  type="number"
                  step="0.0001"
                  min="0"
                  placeholder="0.0000"
                  value={formData.roninPrice}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    roninPrice: e.target.value 
                  }))}
                />
              </div>
              
              <div>
                <label className="block text-sm text-gray-400 mb-2">
                  Insurance (RON)
                </label>
                <Input
                  type="number"
                  step="0.0001"
                  min="0"
                  placeholder={`${(totalRentalPrice * 0.5).toFixed(4)}`}
                  value={insurancePrice}
                  onChange={(e) => setInsurancePrice(e.target.value)}
                />
                <div className="text-xs text-gray-500 mt-1">
                  Suggested: {(totalRentalPrice * 0.5).toFixed(4)} RON (50% of total)
                </div>
              </div>
            </div>
          </div>

          {/* Duration */}
          <div>
            <label className="block text-sm text-gray-400 mb-2 flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Rental Duration
            </label>
            <Select
              value={formData.rentalPeriod.toString()}
              onValueChange={(value) => setFormData(prev => ({ 
                ...prev, 
                rentalPeriod: parseInt(value) 
              }))}
            >
              {durationOptions.map((option) => (
                <Select.Item key={option.value} value={option.value.toString()}>
                  {option.label}
                </Select.Item>
              ))}
            </Select>
          </div>

          {/* Delegation Terms */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-white flex items-center gap-2">
              <Shield className="w-4 h-4" />
              Delegation Terms
            </h3>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">
                Allowed Tasks
              </label>
              <Select
                value={formData.delegatedTask.toString()}
                onValueChange={(value) => setFormData(prev => ({ 
                  ...prev, 
                  delegatedTask: parseInt(value) as EDelegatedTaskType 
                }))}
              >
                {delegatedTaskOptions.map((option) => (
                  <Select.Item key={option.value} value={option.value.toString()}>
                    {option.label}
                  </Select.Item>
                ))}
              </Select>
            </div>

            <div>
              <label className="block text-sm text-gray-400 mb-2">
                Reward Distribution
              </label>
              <Select
                value={formData.rewardDistribution.toString()}
                onValueChange={(value) => setFormData(prev => ({ 
                  ...prev, 
                  rewardDistribution: parseInt(value) as ERewardDistributionType 
                }))}
              >
                {rewardDistributionOptions.map((option) => (
                  <Select.Item key={option.value} value={option.value.toString()}>
                    {option.label}
                  </Select.Item>
                ))}
              </Select>
            </div>

            {formData.rewardDistribution === ERewardDistributionType.SHARED && (
              <div>
                <label className="block text-sm text-gray-400 mb-2">
                  Delegatee Daily Feathers
                </label>
                <Input
                  type="number"
                  min="1"
                  max="100"
                  placeholder="50"
                  value={formData.sharedRewardAmount || ""}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    sharedRewardAmount: parseInt(e.target.value) || undefined 
                  }))}
                />
                <div className="text-xs text-gray-500 mt-1">
                  Daily feathers amount for the renter (1-100)
                </div>
              </div>
            )}
          </div>

          {/* Price Summary */}
          <div className="bg-stone-700/30 rounded-lg p-4">
            <h3 className="text-sm font-medium text-white mb-3 flex items-center gap-2">
              <Calculator className="w-4 h-4" />
              Price Summary
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Daily Rate:</span>
                <span className="text-white">{dailyRate.toFixed(4)} RON</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Duration:</span>
                <span className="text-white">{durationInDays} day(s)</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Total Rental:</span>
                <span className="text-white">{totalRentalPrice.toFixed(4)} RON</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Insurance Required:</span>
                <span className="text-white">{insuranceAmount.toFixed(4)} RON</span>
              </div>
              <div className="border-t border-stone-600 pt-2 flex justify-between font-medium">
                <span className="text-gray-300">Renter Pays:</span>
                <span className="text-yellow-400">
                  {(totalRentalPrice + insuranceAmount).toFixed(4)} RON
                </span>
              </div>
            </div>
          </div>

          {/* Errors */}
          {errors.length > 0 && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
              <div className="text-red-400 text-sm font-medium mb-1">
                Please fix the following errors:
              </div>
              <ul className="text-red-300 text-sm space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}
        </div>

        <Dialog.Footer>
          <Button
            appearance="outline"
            onPress={handleClose}
            isDisabled={isCreating}
          >
            Cancel
          </Button>
          <Button
            onPress={handleSubmit}
            isDisabled={isCreating || !chickenTokenId}
            className="bg-yellow-500 hover:bg-yellow-600 text-black"
          >
            {isCreating ? "Listing..." : "List for Rent"}
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog>
  );
}
