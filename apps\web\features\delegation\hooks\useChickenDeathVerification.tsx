"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Address } from "viem";
import { useStateContext } from "@/providers/app/state";
import useBlockchain from "@/lib/hooks/useBlockchain";

/**
 * Chicken death verification result interface
 */
interface IChickenDeathVerification {
  isDead: boolean;
  isVerified: boolean;
  confidence: "high" | "low";
  error?: string;
  requiresReview?: boolean;
  apiStatus?: boolean;
  contractStatus?: boolean;
  verificationMethod: "api" | "contract" | "dual" | "genesis";
}

/**
 * Battle stats API response interface
 */
interface IBattleStatsResponse {
  state: "alive" | "dead" | "fainted";
  // Add other fields as needed
}

/**
 * Hook for chicken death verification using dual verification approach
 * Implements the verification logic described in the rental system documentation
 */
export const useChickenDeathVerification = () => {
  const { publicClient } = useStateContext();
  const { blockchainQuery } = useBlockchain();

  const [isVerifying, setIsVerifying] = useState(false);

  // Get contract addresses and ABIs from blockchain config
  const chickenLegacyAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.chicken_legacy_address
    : ("" as Address);
  const chickenLegacyAbi = blockchainQuery.isSuccess
    ? blockchainQuery.data?.chicken_legacy_abi
    : undefined;

  // Chicken type threshold (from documentation)
  const CHICKEN_GENESIS_THRESHOLD = 10000; // Adjust based on actual threshold

  /**
   * Check chicken death status via battle stats API
   */
  const checkApiDeathStatus = async (tokenId: number): Promise<boolean> => {
    try {
      const response = await fetch(`/api/proxy/game?tokenId=${tokenId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch battle stats");
      }
      const battleStats: IBattleStatsResponse = await response.json();
      return battleStats.state === "dead";
    } catch (error) {
      console.error("API death check failed:", error);
      throw error;
    }
  };

  /**
   * Check chicken death status via smart contract ownerOf call
   * Only applicable for Legacy chickens (tokenId > CHICKEN_GENESIS_THRESHOLD)
   */
  const checkContractDeathStatus = async (tokenId: number): Promise<boolean> => {
    try {
      // Genesis chickens cannot die, so always return false
      if (tokenId <= CHICKEN_GENESIS_THRESHOLD) {
        return false;
      }

      if (!publicClient || !chickenLegacyAddress || !chickenLegacyAbi) {
        throw new Error("Contract not configured");
      }

      // Try to call ownerOf - if it fails, chicken is dead/burned
      await publicClient.readContract({
        address: chickenLegacyAddress,
        abi: chickenLegacyAbi,
        functionName: "ownerOf",
        args: [BigInt(tokenId)],
      });

      // If ownerOf succeeds, chicken is alive
      return false;
    } catch (error) {
      // If ownerOf fails, chicken is dead/burned
      return true;
    }
  };

  /**
   * Perform comprehensive chicken death verification
   * Implements the dual verification logic from the documentation
   */
  const verifyChickenDeath = async (
    tokenId: number
  ): Promise<IChickenDeathVerification> => {
    setIsVerifying(true);

    try {
      // Genesis chickens cannot die, so always return alive
      if (tokenId <= CHICKEN_GENESIS_THRESHOLD) {
        return {
          isDead: false,
          isVerified: true,
          confidence: "high",
          verificationMethod: "genesis",
        };
      }

      // For Legacy chickens, perform dual verification
      let apiStatus: boolean | undefined;
      let contractStatus: boolean | undefined;
      let apiError: Error | undefined;
      let contractError: Error | undefined;

      // Check API status
      try {
        apiStatus = await checkApiDeathStatus(tokenId);
      } catch (error) {
        apiError = error as Error;
      }

      // Check contract status
      try {
        contractStatus = await checkContractDeathStatus(tokenId);
      } catch (error) {
        contractError = error as Error;
      }

      // Handle verification results
      if (apiStatus !== undefined && contractStatus !== undefined) {
        // Both checks succeeded
        if (apiStatus === contractStatus) {
          // Both agree - high confidence
          return {
            isDead: apiStatus,
            isVerified: true,
            confidence: "high",
            apiStatus,
            contractStatus,
            verificationMethod: "dual",
          };
        } else {
          // Mismatch - prefer contract result but flag for review
          return {
            isDead: contractStatus,
            isVerified: false,
            confidence: "low",
            requiresReview: true,
            apiStatus,
            contractStatus,
            verificationMethod: "dual",
            error: "API and contract verification mismatch",
          };
        }
      } else if (contractStatus !== undefined) {
        // Only contract check succeeded
        return {
          isDead: contractStatus,
          isVerified: true,
          confidence: "high",
          contractStatus,
          verificationMethod: "contract",
          error: apiError ? `API check failed: ${apiError.message}` : undefined,
        };
      } else if (apiStatus !== undefined) {
        // Only API check succeeded
        return {
          isDead: apiStatus,
          isVerified: false,
          confidence: "low",
          requiresReview: true,
          apiStatus,
          verificationMethod: "api",
          error: contractError
            ? `Contract check failed: ${contractError.message}`
            : undefined,
        };
      } else {
        // Both checks failed
        return {
          isDead: false, // Default to alive when uncertain
          isVerified: false,
          confidence: "low",
          requiresReview: true,
          verificationMethod: "dual",
          error: "Both API and contract verification failed",
        };
      }
    } catch (error) {
      console.error("Death verification failed:", error);
      return {
        isDead: false, // Default to alive when uncertain
        isVerified: false,
        confidence: "low",
        requiresReview: true,
        verificationMethod: "dual",
        error: error instanceof Error ? error.message : "Unknown error",
      };
    } finally {
      setIsVerifying(false);
    }
  };

  /**
   * React Query hook for chicken death verification with caching
   */
  const useChickenDeathQuery = (tokenId: number, enabled = true) => {
    return useQuery({
      queryKey: ["chicken-death-verification", tokenId],
      queryFn: () => verifyChickenDeath(tokenId),
      enabled: enabled && !!tokenId,
      staleTime: 30000, // 30 seconds
      refetchOnWindowFocus: false,
      retry: (failureCount, error) => {
        // Don't retry if it's a verification mismatch
        if (error instanceof Error && error.message.includes("mismatch")) {
          return false;
        }
        return failureCount < 2;
      },
    });
  };

  /**
   * Batch verification for multiple chickens
   */
  const verifyMultipleChickens = async (
    tokenIds: number[]
  ): Promise<Record<number, IChickenDeathVerification>> => {
    const results: Record<number, IChickenDeathVerification> = {};

    // Process in parallel but limit concurrency
    const batchSize = 5;
    for (let i = 0; i < tokenIds.length; i += batchSize) {
      const batch = tokenIds.slice(i, i + batchSize);
      const batchPromises = batch.map(async (tokenId) => {
        const result = await verifyChickenDeath(tokenId);
        return { tokenId, result };
      });

      const batchResults = await Promise.all(batchPromises);
      batchResults.forEach(({ tokenId, result }) => {
        results[tokenId] = result;
      });
    }

    return results;
  };

  return {
    verifyChickenDeath,
    useChickenDeathQuery,
    verifyMultipleChickens,
    isVerifying,
    checkApiDeathStatus,
    checkContractDeathStatus,
    CHICKEN_GENESIS_THRESHOLD,
  };
};
