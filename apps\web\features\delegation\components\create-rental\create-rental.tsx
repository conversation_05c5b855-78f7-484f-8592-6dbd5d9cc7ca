"use client";

import { ChickenIcon } from "@/features/dapp/components/icon/chicken.icon";
import { useStateContext } from "@/providers/app/state";
import {
  AlertCircle,
  AlertTriangle,
  BarChart3,
  Calculator,
  CheckCircle,
  Circle,
  Coins,
  DollarSign,
  Lightbulb,
  Loader2,
  PartyPopper,
  Settings,
  Star,
  TrendingUp,
  Zap,
} from "lucide-react";
import { useState, useEffect } from "react";
import { Button, Checkbox, Modal, TextField } from "ui";
import {
  EDelegatedTaskType,
  ERewardDistributionType,
  ICreateRentalFormData,
  calculateMarketplaceFee,
  calculateEarningsAfterFees,
  formatMarketplaceFeePercentage,
} from "../../types/delegation.types";
import { useMarketplaceFee } from "../../hooks/useMarketplaceFee";
import { RewardDistributionSelector } from "../shared/reward-distribution-selector";
import { RewardShareSlider } from "../shared/reward-share-slider";
import { TaskTypeSelector } from "../shared/task-type-selector";
import { ChickenSelectionDialog } from "./chicken-selection-dialog";
import { useCreateRental } from "../../hooks/useCreateRental";
import { IChickenMetadata, IAttribute } from "@/lib/types/chicken.types";
import { useChickensForDelegation } from "../../hooks/useChickensForDelegation";

interface ICreateRentalProps {
  preSelectedChickenId?: number | null;
  onSuccess?: () => void;
}

export function CreateRental({
  preSelectedChickenId,
  onSuccess,
}: ICreateRentalProps = {}) {
  const { address: currentAddress } = useStateContext();
  const { executeCreateRental, isCreating } = useCreateRental();
  const { chickens: availableChickens, isLoading: chickensLoading } =
    useChickensForDelegation();
  const { feePercentage } = useMarketplaceFee();

  const [formData, setFormData] = useState<ICreateRentalFormData>({
    chickenTokenId: null,
    isDirectDelegation: false,
    renterAddress: "",
    roninPrice: "1.0",
    rentalPeriod: 86400, // 1 day in seconds
    rewardDistribution: ERewardDistributionType.DELEGATOR_ONLY,
    delegatedTask: EDelegatedTaskType.BOTH,
    sharedRewardAmount: 5, // Default to 5 feathers for delegatee
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isChickenDialogOpen, setIsChickenDialogOpen] = useState(false);
  const [selectedChicken, setSelectedChicken] = useState<{
    tokenId: number;
    image: string;
    metadata?: IChickenMetadata;
    type?: string;
  } | null>(null);
  const [validationErrors, setValidationErrors] = useState<{
    roninPrice?: string;
    renterAddress?: string;
    sharedRewardAmount?: string;
  }>({});
  const [showSuccess, setShowSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [agreed, setAgreed] = useState(false);

  // Custom duration state
  const [isCustomDuration, setIsCustomDuration] = useState(false);
  const [customDuration, setCustomDuration] = useState({
    days: 0,
  });
  const [customDurationErrors, setCustomDurationErrors] = useState<{
    days?: string;
    hours?: string;
    minutes?: string;
    total?: string;
  }>({});

  // Effect to handle pre-selected chicken
  useEffect(() => {
    if (preSelectedChickenId && availableChickens.length > 0) {
      const preSelectedChicken = availableChickens.find(
        (chicken) => chicken.tokenId === preSelectedChickenId
      );

      if (preSelectedChicken) {
        const chickenData = {
          tokenId: preSelectedChicken.tokenId,
          image: preSelectedChicken.image,
          metadata: preSelectedChicken.metadata,
          type: preSelectedChicken.type,
        };
        setSelectedChicken(chickenData);
        updateFormData({ chickenTokenId: preSelectedChicken.tokenId });
      }
    }
  }, [preSelectedChickenId, availableChickens]);

  const updateFormData = (updates: Partial<ICreateRentalFormData>) => {
    setFormData((prev) => ({ ...prev, ...updates }));
  };

  const handleChickenSelect = (chicken: {
    tokenId: number;
    image: string;
    metadata?: IChickenMetadata;
    type?: string;
  }) => {
    setSelectedChicken(chicken);
    updateFormData({ chickenTokenId: chicken.tokenId });
  };

  // Validation functions
  const validatePrice = (price: string): string | undefined => {
    if (!price || price.trim() === "") return "Daily rate is required";
    const numPrice = parseFloat(price);
    if (isNaN(numPrice)) return "Please enter a valid number";
    if (numPrice <= 0) return "Daily rate must be greater than 0";
    if (numPrice > 100) return "Daily rate seems too high (max 100 RON/day)";
    return undefined;
  };

  const validateAddress = (address: string): string | undefined => {
    if (!address || address.trim() === "") return "Address is required";
    if (!address.startsWith("0x")) return "Address must start with 0x";
    if (address.length !== 42) return "Address must be 42 characters long";

    // Check if the address is the same as the current connected address
    if (
      currentAddress &&
      address.toLowerCase() === currentAddress.toLowerCase()
    ) {
      return "You cannot delegate to yourself";
    }

    return undefined;
  };

  const validateRewardShare = (feathers: number): string | undefined => {
    const maxFeathers = getSelectedChickenDailyFeathers();
    if (feathers < 1) return "Minimum share is 1 feather";
    if (feathers >= maxFeathers)
      return `Maximum share is ${maxFeathers - 1} feathers (owner must get at least 1)`;
    return undefined;
  };

  // Custom duration validation
  const validateCustomDurationField = (
    value: number,
    field: string,
    max: number
  ): string | undefined => {
    if (value < 0) return `${field} cannot be negative`;
    if (value > max) return `${field} cannot exceed ${max}`;
    if (!Number.isInteger(value)) return `${field} must be a whole number`;
    return undefined;
  };

  const validateCustomDuration = (
    days: number
  ): {
    days?: string;
    total?: string;
  } => {
    const errors: {
      days?: string;
      total?: string;
    } = {};

    // Validate days field
    const daysError = validateCustomDurationField(days, "Days", 365);
    if (daysError) errors.days = daysError;

    // Validate total duration
    const totalSeconds = days * 86400;
    if (totalSeconds === 0) {
      errors.total = "Duration must be at least 1 day";
    } else if (totalSeconds > 7776000) {
      // 90 days
      errors.total = "Maximum duration is 90 days";
    }

    return errors;
  };

  const calculateCustomDurationSeconds = (days: number): number => {
    return days * 86400;
  };

  // Enhanced input handlers with validation
  const handlePriceChange = (value: string) => {
    updateFormData({ roninPrice: value });
    const error = validatePrice(value);
    setValidationErrors((prev) => ({ ...prev, roninPrice: error }));
  };

  const handleAddressChange = (value: string) => {
    updateFormData({ renterAddress: value });
    const error = validateAddress(value);
    setValidationErrors((prev) => ({ ...prev, renterAddress: error }));
  };

  const handleRewardShareChange = (value: number) => {
    updateFormData({ sharedRewardAmount: value });
    const error = validateRewardShare(value);
    setValidationErrors((prev) => ({ ...prev, sharedRewardAmount: error }));
  };

  // Get selected chicken's daily feathers
  const getSelectedChickenDailyFeathers = (): number => {
    if (!selectedChicken?.metadata?.attributes) return 10; // Default fallback

    const dailyFeathersAttribute = selectedChicken.metadata.attributes.find(
      (attr: IAttribute) => attr.trait_type === "Daily Feathers"
    );

    return Number(dailyFeathersAttribute?.value) || 10;
  };

  // Custom duration handlers
  const handleCustomDurationChange = (value: string) => {
    const numValue = parseInt(value) || 0;
    const newCustomDuration = { days: numValue };
    setCustomDuration(newCustomDuration);

    // Validate and update errors
    const errors = validateCustomDuration(newCustomDuration.days);
    setCustomDurationErrors(errors);

    // Update form data if no errors
    if (Object.keys(errors).length === 0) {
      const totalSeconds = calculateCustomDurationSeconds(
        newCustomDuration.days
      );
      updateFormData({ rentalPeriod: totalSeconds });
    }
  };

  const handleDurationOptionSelect = (value: number) => {
    if (value === -1) {
      // Custom duration selected
      setIsCustomDuration(true);
      // Don't update rentalPeriod yet, wait for custom input
    } else {
      // Predefined duration selected
      setIsCustomDuration(false);
      setCustomDuration({ days: 0 });
      setCustomDurationErrors({});
      updateFormData({ rentalPeriod: value });
    }
  };

  // Format price display
  const formatPrice = (price: string): string => {
    const num = parseFloat(price);
    if (isNaN(num)) return price;
    return num.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 4,
    });
  };

  const calculateEstimatedEarnings = () => {
    if (!selectedChicken || formData.isDirectDelegation) return null;

    const dailyRate = parseFloat(formData.roninPrice) || 0;
    const durationDays = formData.rentalPeriod / 86400;
    const totalRentalIncome = dailyRate * durationDays;
    let dailyFeathers =
      Number(
        selectedChicken.metadata?.attributes?.find(
          (attr: IAttribute) => attr.trait_type === "Daily Feathers"
        )?.value
      ) || 10;

    // If delegated task is gameplay only, no feathers are generated
    if (formData.delegatedTask === EDelegatedTaskType.GAMEPLAY) {
      dailyFeathers = 0;
    }

    const totalFeathers = dailyFeathers * durationDays;

    // Calculate feather distribution based on reward settings
    let ownerFeathers = 0;
    let renterFeathers = 0;

    if (
      formData.rewardDistribution === ERewardDistributionType.DELEGATOR_ONLY
    ) {
      ownerFeathers = totalFeathers;
      renterFeathers = 0;
    } else if (
      formData.rewardDistribution === ERewardDistributionType.DELEGATEE_ONLY
    ) {
      ownerFeathers = 0;
      renterFeathers = totalFeathers;
    } else if (formData.rewardDistribution === ERewardDistributionType.SHARED) {
      // Use actual feather amount per day, not percentage
      const dailyRenterFeathers = formData.sharedRewardAmount;
      renterFeathers = dailyRenterFeathers * durationDays;
      ownerFeathers = totalFeathers - renterFeathers;
    }

    // Calculate marketplace fees and net earnings
    const marketplaceFee = calculateMarketplaceFee(
      totalRentalIncome,
      feePercentage
    );
    const netRentalIncome = calculateEarningsAfterFees(
      totalRentalIncome,
      feePercentage
    );

    return {
      rentalIncome: totalRentalIncome,
      marketplaceFee,
      netRentalIncome,
      dailyRate,
      ownerFeathers,
      renterFeathers,
      totalFeathers,
      dailyFeathers,
      durationDays,
    };
  };

  // Quick preset configurations
  const quickPresets = [
    {
      name: "Quick Rental",
      description: "3 days, renter keeps rewards",
      icon: Zap,
      config: {
        rentalPeriod: 259200,
        rewardDistribution: ERewardDistributionType.DELEGATEE_ONLY,
        delegatedTask: EDelegatedTaskType.BOTH,
      },
    },
    {
      name: "Max Profit",
      description: "Keep all rewards, premium price",
      icon: TrendingUp,
      config: {
        rentalPeriod: 604800,
        rewardDistribution: ERewardDistributionType.DELEGATOR_ONLY,
        delegatedTask: EDelegatedTaskType.BOTH,
      },
    },
    {
      name: "Attractive Deal",
      description: "1 week, renter keeps rewards",
      icon: BarChart3,
      config: {
        rentalPeriod: 604800,
        rewardDistribution: ERewardDistributionType.DELEGATEE_ONLY,
        delegatedTask: EDelegatedTaskType.BOTH,
      },
    },
  ];

  const applyPreset = (preset: (typeof quickPresets)[0]) => {
    updateFormData(preset.config);
  };

  // Progress tracking
  const getFormProgress = () => {
    const steps = [
      {
        id: "chicken",
        label: "Select Chicken",
        completed: !!formData.chickenTokenId,
      },
      {
        id: "pricing",
        label: "Set Terms",
        completed: formData.isDirectDelegation
          ? !!formData.renterAddress
          : !!formData.roninPrice && parseFloat(formData.roninPrice) > 0,
      },
      { id: "settings", label: "Configure Settings", completed: true }, // Always completed as has defaults
    ];

    const completedSteps = steps.filter((step) => step.completed).length;
    return { steps, completedSteps, totalSteps: steps.length };
  };

  const { steps, completedSteps } = getFormProgress();

  // Show confirmation dialog
  const handleShowConfirmation = () => {
    setIsConfirmDialogOpen(true);
    setAgreed(false);
  };

  // Handle confirmed submission
  const handleConfirmedSubmit = async () => {
    setIsLoading(true);
    setSubmitError(null);
    setIsConfirmDialogOpen(false);

    try {
      // Run comprehensive validation before submission
      const currentValidationErrors: typeof validationErrors = {};

      // Validate chicken selection
      if (!formData.chickenTokenId) {
        throw new Error("Please select a chicken to rent or delegate");
      }

      // Validate delegation type specific fields
      if (formData.isDirectDelegation) {
        const addressError = validateAddress(formData.renterAddress);
        if (addressError) {
          currentValidationErrors.renterAddress = addressError;
        }
      } else {
        const priceError = validatePrice(formData.roninPrice);
        if (priceError) {
          currentValidationErrors.roninPrice = priceError;
        }
      }

      // Validate shared reward amount if applicable
      if (formData.rewardDistribution === ERewardDistributionType.SHARED) {
        const rewardError = validateRewardShare(formData.sharedRewardAmount);
        if (rewardError) {
          currentValidationErrors.sharedRewardAmount = rewardError;
        }
      }

      // Validate custom duration if selected
      if (isCustomDuration) {
        const durationErrors = validateCustomDuration(customDuration.days);
        if (Object.keys(durationErrors).length > 0) {
          throw new Error(
            `Custom duration errors: ${Object.values(durationErrors).join(", ")}`
          );
        }
      }

      // Check if there are any validation errors
      const hasErrors = Object.values(currentValidationErrors).some(
        (error) => error
      );
      if (hasErrors) {
        console.error("Validation errors found:", currentValidationErrors);
        setValidationErrors(currentValidationErrors);
        const errorMessages = Object.entries(currentValidationErrors)
          .filter(([, error]) => error)
          .map(([field, error]) => `${field}: ${error}`)
          .join(", ");
        throw new Error(`Please fix validation errors: ${errorMessages}`);
      }

      // Clear any existing validation errors
      setValidationErrors({});

      // Use the new hook to create the rental
      const result = await executeCreateRental(formData);

      if (result.success) {
        // Show success feedback
        setShowSuccess(true);

        // Call onSuccess callback if provided
        if (onSuccess) {
          onSuccess();
        }

        // Auto-hide success message after 5 seconds
        setTimeout(() => {
          setShowSuccess(false);
        }, 5000);

        // Reset form on success
        setFormData({
          chickenTokenId: null,
          isDirectDelegation: false,
          renterAddress: "",
          roninPrice: "1.0",
          rentalPeriod: 86400,
          rewardDistribution: ERewardDistributionType.DELEGATOR_ONLY,
          delegatedTask: EDelegatedTaskType.BOTH,
          sharedRewardAmount: 5,
        });
        setSelectedChicken(null);
        setValidationErrors({});
        // Reset custom duration state
        setIsCustomDuration(false);
        setCustomDuration({ days: 0 });
        setCustomDurationErrors({});
        // Reset confirmation state
        setAgreed(false);
      } else {
        throw new Error(result.error || "Failed to create rental");
      }
    } catch (error) {
      console.error("Failed to create rental:", error);
      setSubmitError(
        error instanceof Error ? error.message : "An unexpected error occurred"
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Handle dialog close
  const handleCloseConfirmDialog = () => {
    setIsConfirmDialogOpen(false);
    setAgreed(false);
  };

  const isFormValid = () => {
    if (!formData.chickenTokenId) return false;
    if (formData.isDirectDelegation && !formData.renterAddress) return false;
    if (
      !formData.isDirectDelegation &&
      (!formData.roninPrice || parseFloat(formData.roninPrice) <= 0)
    )
      return false;
    if (
      formData.rewardDistribution === ERewardDistributionType.SHARED &&
      (formData.sharedRewardAmount < 1 ||
        formData.sharedRewardAmount >= getSelectedChickenDailyFeathers())
    )
      return false;

    // Validate custom duration if selected
    if (isCustomDuration) {
      const errors = validateCustomDuration(customDuration.days);
      if (Object.keys(errors).length > 0) return false;
    }

    // Ensure a valid duration is set
    if (!isCustomDuration && !formData.rentalPeriod) return false;

    return true;
  };

  const durationOptions = [
    {
      value: 86400,
      label: "1 Day",
      isPopular: false,
      description: "Short term",
    },
    {
      value: 259200,
      label: "3 Days",
      isPopular: true,
      description: "Most popular",
    },
    {
      value: 604800,
      label: "1 Week",
      isPopular: true,
      description: "Great value",
    },
    {
      value: 1209600,
      label: "2 Weeks",
      isPopular: false,
      description: "Extended",
    },
    {
      value: 2592000,
      label: "1 Month",
      isPopular: false,
      description: "Long term",
    },
    {
      value: -1,
      label: "Set Duration",
      isPopular: false,
      description: "Set your own",
    },
  ];

  // Show loading state while chickens data is loading
  if (chickensLoading) {
    return (
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-2">Create Listing</h2>
          <p className="text-gray-400">
            List your chicken for rent or delegate it directly to another player
          </p>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3">
            <Loader2 className="w-6 h-6 animate-spin text-blue-400" />
            <span className="text-white">Loading chicken data...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">Create Listing</h2>
        <p className="text-gray-400">
          List your chicken for rent or delegate it directly to another player
        </p>
      </div>

      {/* Success Feedback */}
      {showSuccess && (
        <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-6 animate-in slide-in-from-top duration-300">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              <PartyPopper className="w-8 h-8 text-green-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-green-400 mb-1">
                {formData.isDirectDelegation
                  ? "Delegation Created!"
                  : "Rental Listed!"}
              </h3>
              <p className="text-green-300">
                {formData.isDirectDelegation
                  ? "Your chicken has been successfully delegated. The delegatee can now use it according to your settings."
                  : "Your chicken is now available for rent on the marketplace. You'll be notified when someone rents it."}
              </p>
            </div>
            <button
              onClick={() => setShowSuccess(false)}
              className="flex-shrink-0 text-green-400 hover:text-green-300 transition-colors"
              aria-label="Close success message"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Error Feedback */}
      {submitError && (
        <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-6">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              <AlertCircle className="w-6 h-6 text-red-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-red-400 mb-1">
                Submission Failed
              </h3>
              <p className="text-red-300">{submitError}</p>
            </div>
            <button
              onClick={() => setSubmitError(null)}
              className="flex-shrink-0 text-red-400 hover:text-red-300 transition-colors"
              aria-label="Close error message"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Progress Indicator */}
      <div className="bg-stone-800 border border-stone-700 rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-white">Setup Progress</h3>
          <span className="text-sm text-gray-400">
            {completedSteps}/{steps.length} completed
          </span>
        </div>
        <div className="flex items-center gap-4">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center gap-2">
              <div className="flex items-center gap-2">
                {step.completed ? (
                  <CheckCircle className="w-5 h-5 text-green-400" />
                ) : (
                  <Circle className="w-5 h-5 text-gray-500" />
                )}
                <span
                  className={`text-sm ${step.completed ? "text-green-400" : "text-gray-400"}`}
                >
                  {step.label}
                </span>
              </div>
              {index < steps.length - 1 && (
                <div className="w-8 h-px bg-gray-600 mx-2" />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Form Sections */}
      <div className="space-y-6">
        {/* Section 1: Chicken Selection */}
        <div className="bg-stone-800 border border-stone-700 rounded-lg p-6">
          <div className="flex items-center gap-2 mb-4">
            <ChickenIcon className="w-5 h-5 text-blue-400" />
            <h3 className="text-lg font-semibold text-white">
              Step 1: Choose Your Chicken
            </h3>
          </div>

          <div className="space-y-4">
            <Button
              className="w-full justify-start bg-stone-700 hover:bg-stone-600 text-white border-stone-600"
              onPress={() => setIsChickenDialogOpen(true)}
              aria-label={
                selectedChicken
                  ? `Selected chicken: ${selectedChicken.metadata?.name || `Chicken #${selectedChicken.tokenId}`}. Click to change selection.`
                  : "Click to select a chicken for rental or delegation"
              }
            >
              <ChickenIcon className="w-4 h-4 mr-2" aria-hidden="true" />
              {selectedChicken
                ? `${selectedChicken.metadata?.name || `Chicken #${selectedChicken.tokenId}`}`
                : "Choose a chicken to rent out"}
            </Button>

            {selectedChicken && (
              <div className="p-4 bg-stone-700 rounded-lg border border-stone-600">
                <div className="flex items-center gap-4">
                  <img
                    src={selectedChicken.image}
                    alt={`Chicken #${selectedChicken.tokenId}`}
                    className="w-16 h-16 rounded-lg object-cover"
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <p className="text-white font-semibold">
                        #{selectedChicken.tokenId}
                      </p>
                      {selectedChicken.type && (
                        <span className="px-2 py-1 text-xs bg-blue-500/20 text-blue-400 rounded border border-blue-500/30">
                          {selectedChicken.type}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-400">
                      {selectedChicken.metadata?.name ||
                        `Chicken #${selectedChicken.tokenId}`}
                    </p>
                  </div>
                  <CheckCircle className="w-5 h-5 text-green-400" />
                </div>
              </div>
            )}

            <p className="text-sm text-gray-400">
              Select one of your available chickens to list for rental or
              delegation
            </p>
          </div>
        </div>

        {/* Quick Presets */}
        {selectedChicken && !formData.isDirectDelegation && (
          <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/30 rounded-lg p-6">
            <div className="flex items-center gap-2 mb-4">
              <Lightbulb className="w-5 h-5 text-yellow-400" />
              <h3 className="text-lg font-semibold text-white">
                Rental Presets
              </h3>
            </div>
            <p className="text-sm text-gray-300 mb-4">
              Choose a preset configuration to get started quickly
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {quickPresets.map((preset) => {
                const Icon = preset.icon;
                return (
                  <div
                    key={preset.name}
                    onClick={() => applyPreset(preset)}
                    className="p-4 bg-stone-800 border border-stone-600 rounded-lg cursor-pointer hover:border-blue-500 transition-all duration-200 hover:scale-105"
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <Icon className="w-4 h-4 text-blue-400" />
                      <span className="font-medium text-white">
                        {preset.name}
                      </span>
                    </div>
                    <p className="text-sm text-gray-400">
                      {preset.description}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Section 2: Rental Terms */}
        <div className="bg-stone-800 border border-stone-700 rounded-lg p-6">
          <div className="flex items-center gap-2 mb-4">
            <DollarSign className="w-5 h-5 text-green-400" />
            <h3 className="text-lg font-semibold text-white">
              Step 2: Set Rental Terms
            </h3>
          </div>

          <div className="space-y-6">
            {/* Delegation Type Toggle */}
            <div
              className="p-4 bg-stone-700 rounded-lg border border-stone-600 cursor-pointer hover:bg-stone-600 transition-colors duration-200"
              onClick={() =>
                updateFormData({
                  isDirectDelegation: !formData.isDirectDelegation,
                })
              }
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-white mb-1">
                    Direct Delegation
                  </h4>
                  <p className="text-sm text-gray-400">
                    Delegate directly to a specific address (free) instead of
                    listing for rent
                  </p>
                </div>
                <Checkbox
                  isSelected={formData.isDirectDelegation}
                  onChange={(checked) =>
                    updateFormData({ isDirectDelegation: checked })
                  }
                />
              </div>
            </div>

            {/* Direct Delegation Address */}
            {formData.isDirectDelegation && (
              <div className="space-y-2">
                <label className="block text-sm font-medium text-white">
                  Delegatee Address
                </label>
                <div className="relative">
                  <TextField
                    placeholder="0x..."
                    value={formData.renterAddress}
                    onChange={handleAddressChange}
                    className={`w-full ${validationErrors.renterAddress ? "border-red-500" : ""}`}
                  />
                  {validationErrors.renterAddress && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <AlertCircle className="w-4 h-4 text-red-400" />
                    </div>
                  )}
                </div>
                {validationErrors.renterAddress ? (
                  <p className="text-sm text-red-400 flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {validationErrors.renterAddress}
                  </p>
                ) : (
                  <p className="text-sm text-gray-400">
                    Enter the wallet address of the person you want to delegate
                    to
                  </p>
                )}
              </div>
            )}

            {/* Daily Rental Rate */}
            {!formData.isDirectDelegation && (
              <div className="space-y-2">
                <label className="block text-sm font-medium text-white">
                  Daily Rental Rate (RON)
                </label>
                <div className="relative">
                  <Coins className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <TextField
                    type="number"
                    placeholder="1.0"
                    value={formData.roninPrice}
                    onChange={handlePriceChange}
                    className={`w-full pl-10 ${validationErrors.roninPrice ? "border-red-500" : ""}`}
                    aria-label="Daily rental rate in RON"
                    aria-describedby={
                      validationErrors.roninPrice ? "price-error" : "price-help"
                    }
                  />
                  {validationErrors.roninPrice && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <AlertCircle className="w-4 h-4 text-red-400" />
                    </div>
                  )}
                </div>
                {validationErrors.roninPrice ? (
                  <p
                    id="price-error"
                    className="text-sm text-red-400 flex items-center gap-1"
                    role="alert"
                  >
                    <AlertCircle className="w-3 h-3" aria-hidden="true" />
                    {validationErrors.roninPrice}
                  </p>
                ) : (
                  <div className="space-y-1">
                    <p id="price-help" className="text-sm text-gray-400">
                      Daily rental rate. Total cost will be calculated based on
                      duration
                    </p>
                    {formData.roninPrice &&
                      !isNaN(parseFloat(formData.roninPrice)) && (
                        <div className="space-y-1">
                          <p className="text-sm text-green-400 flex items-center gap-1">
                            <TrendingUp
                              className="w-3 h-3"
                              aria-hidden="true"
                            />
                            Daily: {formatPrice(formData.roninPrice)} RON
                          </p>
                          <p className="text-sm text-blue-400 flex items-center gap-1">
                            <TrendingUp
                              className="w-3 h-3"
                              aria-hidden="true"
                            />
                            Total:{" "}
                            {formatPrice(
                              (
                                parseFloat(formData.roninPrice) *
                                (formData.rentalPeriod / 86400)
                              ).toString()
                            )}{" "}
                            RON
                          </p>
                          {(() => {
                            const totalAmount =
                              parseFloat(formData.roninPrice) *
                              (formData.rentalPeriod / 86400);
                            const marketplaceFee = calculateMarketplaceFee(
                              totalAmount,
                              feePercentage
                            );
                            const earningsAfterFees =
                              calculateEarningsAfterFees(
                                totalAmount,
                                feePercentage
                              );

                            return (
                              <div className="pt-4">
                                <div className="bg-stone-800/50 border border-stone-700 rounded-lg p-3 space-y-2">
                                  <div className="flex items-center justify-between text-xs">
                                    <span className="text-gray-400">
                                      Marketplace Fee (
                                      {formatMarketplaceFeePercentage(
                                        feePercentage
                                      )}
                                      ):
                                    </span>
                                    <span className="text-red-400">
                                      -{formatPrice(marketplaceFee.toString())}{" "}
                                      RON
                                    </span>
                                  </div>
                                  <div className="flex items-center justify-between text-sm font-medium border-t border-stone-600 pt-2">
                                    <span className="text-yellow-400">
                                      Your Earnings:
                                    </span>
                                    <span className="text-yellow-400">
                                      {formatPrice(
                                        earningsAfterFees.toString()
                                      )}{" "}
                                      RON
                                    </span>
                                  </div>
                                </div>
                              </div>
                            );
                          })()}
                        </div>
                      )}
                  </div>
                )}
              </div>
            )}

            {/* Rental Duration */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-white">
                Rental Duration
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-3">
                {durationOptions.map((option) => (
                  <div
                    key={option.value}
                    onClick={() => handleDurationOptionSelect(option.value)}
                    className={`relative p-3 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                      (option.value === -1 && isCustomDuration) ||
                      (option.value !== -1 &&
                        !isCustomDuration &&
                        formData.rentalPeriod === option.value)
                        ? "border-blue-500 bg-blue-500/10"
                        : "border-stone-600 bg-stone-700 hover:border-stone-500"
                    }`}
                  >
                    {/* Popular Badge */}
                    {option.isPopular && (
                      <div className="absolute -top-2 -right-2">
                        <div className="bg-yellow-500 text-black text-xs font-bold px-2 py-1 rounded-full flex items-center gap-1">
                          <Star className="w-3 h-3" />
                          Popular
                        </div>
                      </div>
                    )}

                    <div className="text-center">
                      <div className="font-medium text-white mb-1">
                        {option.label}
                      </div>
                      <div className="text-xs text-gray-400">
                        {option.description}
                      </div>
                    </div>

                    {((option.value === -1 && isCustomDuration) ||
                      (option.value !== -1 &&
                        !isCustomDuration &&
                        formData.rentalPeriod === option.value)) && (
                      <div className="absolute bottom-1 right-1">
                        <CheckCircle className="w-4 h-4 text-blue-400" />
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Custom Duration Inputs */}
              {isCustomDuration && (
                <div className="p-4 bg-stone-700 rounded-lg border border-stone-600 space-y-4">
                  <h4 className="font-medium text-white">Set Duration</h4>
                  <div className="max-w-xs">
                    <div>
                      <label className="block text-sm font-medium text-white mb-2">
                        Days
                      </label>
                      <TextField
                        type="number"
                        placeholder="Enter number of days"
                        value={customDuration.days.toString()}
                        onChange={(value) => handleCustomDurationChange(value)}
                        className={`w-full ${customDurationErrors.days ? "border-red-500" : ""}`}
                      />
                      {customDurationErrors.days && (
                        <p className="text-sm text-red-400 mt-1 flex items-center gap-1">
                          <AlertCircle className="w-3 h-3" />
                          {customDurationErrors.days}
                        </p>
                      )}
                    </div>
                  </div>

                  {customDurationErrors.total && (
                    <p className="text-sm text-red-400 flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {customDurationErrors.total}
                    </p>
                  )}

                  {!customDurationErrors.total && customDuration.days > 0 && (
                    <p className="text-sm text-green-400 flex items-center gap-1">
                      <CheckCircle className="w-3 h-3" />
                      Total duration:{" "}
                      {`${customDuration.days} day${customDuration.days > 1 ? "s" : ""}`}
                    </p>
                  )}
                </div>
              )}

              <p className="text-sm text-gray-400">
                Choose how long the renter can use your chicken. Popular options
                are highlighted, or set a custom duration.
              </p>
            </div>
          </div>
        </div>

        {/* Section 3: Delegation Settings */}
        <div className="bg-stone-800 border border-stone-700 rounded-lg p-6">
          <div className="flex items-center gap-2 mb-4">
            <Settings className="w-5 h-5 text-purple-400" />
            <h3 className="text-lg font-semibold text-white">
              Step 3: Configure Delegation
            </h3>
          </div>

          <div className="space-y-8">
            {/* Task Type */}
            <TaskTypeSelector
              value={formData.delegatedTask}
              onChange={(value) => updateFormData({ delegatedTask: value })}
            />

            {/* Reward Distribution */}
            <RewardDistributionSelector
              value={formData.rewardDistribution}
              onChange={(value) =>
                updateFormData({ rewardDistribution: value })
              }
            />

            {/* Shared Reward Amount */}
            {formData.rewardDistribution === ERewardDistributionType.SHARED && (
              <div className="p-4 bg-stone-700 rounded-lg border border-stone-600">
                <RewardShareSlider
                  value={formData.sharedRewardAmount}
                  onChange={handleRewardShareChange}
                  maxFeathers={getSelectedChickenDailyFeathers()}
                  error={validationErrors.sharedRewardAmount}
                />
              </div>
            )}
          </div>
        </div>

        {/* Earnings Calculator */}
        {selectedChicken &&
          !formData.isDirectDelegation &&
          formData.roninPrice && (
            <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/30 rounded-lg p-6">
              <div className="flex items-center gap-2 mb-4">
                <Calculator className="w-5 h-5 text-green-400" />
                <h3 className="text-lg font-semibold text-white">
                  Estimated Earnings
                </h3>
              </div>

              {(() => {
                const earnings = calculateEstimatedEarnings();
                if (!earnings) return null;

                return (
                  <div className="space-y-4">
                    {/* RON Earnings Breakdown */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-stone-800 rounded-lg p-4">
                        <div className="text-sm text-gray-400 mb-1">
                          Daily Rate
                        </div>
                        <div className="text-xl font-bold text-green-400">
                          {formatPrice(earnings.dailyRate.toString())} RON
                        </div>
                        <div className="text-xs text-gray-400">per day</div>
                      </div>

                      <div className="bg-stone-800 rounded-lg p-4">
                        <div className="text-sm text-gray-400 mb-1">
                          Total Income
                        </div>
                        <div className="text-xl font-bold text-blue-400">
                          {formatPrice(earnings.rentalIncome.toString())} RON
                        </div>
                        <div className="text-xs text-gray-400">
                          {earnings.durationDays.toFixed(1)} days
                        </div>
                      </div>

                      <div className="bg-stone-800 rounded-lg p-4">
                        <div className="text-sm text-gray-400 mb-1">
                          Your Earnings
                        </div>
                        <div className="text-xl font-bold text-yellow-400">
                          {formatPrice(earnings.netRentalIncome.toString())} RON
                        </div>
                        <div className="text-xs text-red-400">
                          -{formatPrice(earnings.marketplaceFee.toString())} RON
                          fee ({formatMarketplaceFeePercentage(feePercentage)})
                        </div>
                      </div>
                    </div>

                    {/* Feathers Breakdown */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-stone-800 rounded-lg p-4">
                        <div className="text-sm text-gray-400 mb-1">
                          Your Feathers
                        </div>
                        <div className="text-xl font-bold text-blue-400">
                          {earnings.ownerFeathers.toLocaleString()}
                        </div>
                        <div className="text-xs text-gray-400">
                          {(
                            (earnings.ownerFeathers / earnings.totalFeathers) *
                            100
                          ).toFixed(0)}
                          % of total
                        </div>
                      </div>

                      <div className="bg-stone-800 rounded-lg p-4">
                        <div className="text-sm text-gray-400 mb-1">
                          Renter&apos;s Feathers
                        </div>
                        <div className="text-xl font-bold text-purple-400">
                          {earnings.renterFeathers.toLocaleString()}
                        </div>
                        <div className="text-xs text-gray-400">
                          {(
                            (earnings.renterFeathers / earnings.totalFeathers) *
                            100
                          ).toFixed(0)}
                          % of total
                        </div>
                      </div>

                      <div className="bg-stone-800 rounded-lg p-4">
                        <div className="text-sm text-gray-400 mb-1">
                          Total Feathers
                        </div>
                        <div className="text-xl font-bold text-yellow-400">
                          {earnings.totalFeathers.toLocaleString()}
                        </div>
                        <div className="text-xs text-gray-400">
                          {earnings.dailyFeathers}/day × {earnings.durationDays}{" "}
                          days
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>
          )}

        {/* Submit Section */}
        <div className="bg-stone-800 border border-stone-700 rounded-lg p-6">
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center gap-2 mb-4">
              <CheckCircle className="w-5 h-5 text-green-400" />
              <h3 className="text-lg font-semibold text-white">
                Ready to Create
              </h3>
            </div>

            {/* Form Summary */}
            <div className="bg-stone-700 rounded-lg p-4 text-left">
              <h4 className="font-medium text-white mb-2">Summary</h4>
              <div className="space-y-1 text-sm text-gray-300">
                <p>
                  • Chicken:{" "}
                  {selectedChicken
                    ? `#${selectedChicken.tokenId}`
                    : "Not selected"}
                </p>
                <p>
                  • Type:{" "}
                  {formData.isDirectDelegation
                    ? "Direct Delegation"
                    : "Rental Listing"}
                </p>
                {!formData.isDirectDelegation && (
                  <div>
                    <p>• Daily Rate: {formData.roninPrice} RON/day</p>
                    <p>
                      • Total Cost:{" "}
                      {formatPrice(
                        (
                          parseFloat(formData.roninPrice) *
                          (formData.rentalPeriod / 86400)
                        ).toString()
                      )}{" "}
                      RON
                    </p>
                  </div>
                )}
                <p>
                  • Duration:{" "}
                  {isCustomDuration
                    ? customDuration.days > 0
                      ? `${customDuration.days} day${customDuration.days > 1 ? "s" : ""}`
                      : "Not set"
                    : durationOptions.find(
                        (d) => d.value === formData.rentalPeriod
                      )?.label || "Not set"}
                </p>
                <p>
                  • Access:{" "}
                  {formData.delegatedTask === EDelegatedTaskType.BOTH
                    ? "Full Access"
                    : formData.delegatedTask === EDelegatedTaskType.DAILY_RUB
                      ? "Daily Rub Only"
                      : "Gameplay Only"}
                </p>
              </div>
            </div>

            <div className="grid place-items-center pt-2">
              <Button
                className="bg-yellow-500 hover:bg-yellow-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-black font-semibold px-12 py-3 text-lg transition-all duration-200 flex items-center gap-2"
                onPress={handleShowConfirmation}
                isDisabled={!isFormValid() || isLoading || isCreating}
                aria-label={
                  isLoading || isCreating
                    ? "Creating rental listing, please wait"
                    : formData.isDirectDelegation
                      ? "Create delegation for selected chicken"
                      : "Create rental listing for selected chicken"
                }
              >
                {(isLoading || isCreating) && (
                  <Loader2 className="w-5 h-5 animate-spin" />
                )}
                {isLoading || isCreating
                  ? "Creating..."
                  : formData.isDirectDelegation
                    ? "Create Delegation"
                    : "Create Listing"}
              </Button>
            </div>

            {!isFormValid() && (
              <p className="text-sm text-red-400">
                Please complete all required fields to continue
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
          <h3 className="font-medium text-blue-400 mb-2">Rental Listings</h3>
          <p className="text-sm text-gray-300">
            List your chicken on the marketplace for others to rent. Set your
            daily rate and duration. Total cost is calculated automatically.
            Renters pay the full amount upfront.
          </p>
        </div>

        <div className="bg-purple-500/10 border border-purple-500/30 rounded-lg p-4">
          <h3 className="font-medium text-purple-400 mb-2">
            Direct Delegation
          </h3>
          <p className="text-sm text-gray-300">
            Delegate your chicken directly to a trusted player for free. Perfect
            for friends, guild members, or strategic partnerships. You maintain
            full control over the terms.
          </p>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <Modal
        isOpen={isConfirmDialogOpen}
        onOpenChange={handleCloseConfirmDialog}
      >
        <Modal.Content size="lg">
          <Modal.Header>
            <Modal.Title>
              {formData.isDirectDelegation
                ? "Confirm Delegation"
                : "Confirm Rental Listing"}
            </Modal.Title>
            <Modal.Description>
              Please review the important information below before proceeding
            </Modal.Description>
          </Modal.Header>

          <Modal.Body className="space-y-6">
            {/* Warning Section */}
            <div className="flex gap-3 p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-yellow-400 flex-shrink-0 mt-0.5" />
              <div className="text-sm">
                <p className="text-yellow-400 font-medium mb-2">
                  ⚠️ Important Warning
                </p>
                <div className="text-gray-300 space-y-2">
                  <p>
                    {formData.isDirectDelegation
                      ? "By delegating your chicken, you are granting another player control over your chicken's activities."
                      : "By listing your chicken for rent, you allow renters to control your chicken's activities during the rental period."}
                  </p>
                  <p>
                    <strong>Potential Risks:</strong>
                  </p>
                  <ul className="list-disc list-inside ml-2 space-y-1">
                    {/* Show death warning only for gameplay tasks */}
                    {(formData.delegatedTask === EDelegatedTaskType.GAMEPLAY ||
                      formData.delegatedTask === EDelegatedTaskType.BOTH) && (
                      <li>Your chicken may be injured or die during battles</li>
                    )}
                    <li>
                      The {formData.isDirectDelegation ? "delegatee" : "renter"}{" "}
                      has full control over{" "}
                      {formData.delegatedTask === EDelegatedTaskType.DAILY_RUB
                        ? "daily rub activities"
                        : formData.delegatedTask === EDelegatedTaskType.GAMEPLAY
                          ? "gameplay decisions"
                          : "all delegated activities"}
                    </li>
                    <li>
                      You cannot intervene during the{" "}
                      {formData.isDirectDelegation ? "delegation" : "rental"}{" "}
                      period
                    </li>
                    <li>
                      Any negative consequences are permanent and cannot be
                      undone
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Disclaimer Section */}
            <div className="flex gap-3 p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
              <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
              <div className="text-sm">
                <p className="text-red-400 font-medium mb-2">📋 Disclaimer</p>
                <div className="text-gray-300 space-y-2">
                  <p>By proceeding, you acknowledge that:</p>
                  <ul className="list-disc list-inside ml-2 space-y-1">
                    <li>
                      You understand all risks involved in{" "}
                      {formData.isDirectDelegation ? "delegation" : "rental"}
                    </li>
                    <li>
                      You trust the{" "}
                      {formData.isDirectDelegation ? "delegatee" : "renter"} to
                      handle your chicken responsibly
                    </li>
                    <li>You accept full responsibility for any outcomes</li>
                    <li>
                      The platform is not liable for any losses or damages
                    </li>
                    <li>All transactions are final and non-refundable</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Agreement Checkbox */}
            <div className="flex items-start gap-3">
              <Checkbox
                isSelected={agreed}
                onChange={setAgreed}
                className="mt-1"
              />
              <label
                className="text-sm text-gray-300 cursor-pointer"
                onClick={() => setAgreed(!agreed)}
              >
                I have read and understood all warnings and disclaimers above. I
                confirm that I want to proceed with
                {formData.isDirectDelegation ? " delegating" : " listing"} my
                chicken
                <strong> #{selectedChicken?.tokenId}</strong> and accept all
                associated risks.
              </label>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button
              appearance="outline"
              onPress={handleCloseConfirmDialog}
              className="text-gray-300 border-gray-600 hover:bg-gray-700"
            >
              Cancel
            </Button>
            <Button
              className="bg-yellow-500 hover:bg-yellow-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-black font-semibold"
              onPress={handleConfirmedSubmit}
              isDisabled={!agreed || isLoading || isCreating}
            >
              {(isLoading || isCreating) && (
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
              )}
              {isLoading || isCreating
                ? "Processing..."
                : formData.isDirectDelegation
                  ? "Confirm Delegation"
                  : "Confirm Listing"}
            </Button>
          </Modal.Footer>
        </Modal.Content>
      </Modal>

      {/* Chicken Selection Dialog */}
      <ChickenSelectionDialog
        isOpen={isChickenDialogOpen}
        onOpenChange={setIsChickenDialogOpen}
        onSelect={handleChickenSelect}
        selectedChickenId={selectedChicken?.tokenId || null}
      />
    </div>
  );
}
