"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Address } from "viem";
import { useStateContext } from "@/providers/app/state";
import useBlockchain from "@/lib/hooks/useBlockchain";
import { chain } from "@/providers/web3/web3-provider";
import { rentalAbi } from "@/providers/web3/abi/rental-abi";
import { useChickenDeathVerification } from "./useChickenDeathVerification";
import { IRentalWithMetadata } from "../types/delegation.types";

/**
 * Insurance claim eligibility interface
 */
interface IInsuranceClaimEligibility {
  canClaim: boolean;
  reason: string;
  recipient: "owner" | "renter" | "none";
  verification?: any; // IChickenDeathVerification type
}

/**
 * Hook for handling insurance claiming process
 * Includes death verification and blockchain transaction execution
 */
export const useClaimInsurance = () => {
  const { publicClient, walletClient, address, Disconnect } = useStateContext();
  const { blockchainQuery } = useBlockchain();
  const { verifyChickenDeath } = useChickenDeathVerification();
  const queryClient = useQueryClient();

  const [isClaiming, setIsClaiming] = useState(false);

  // Get rental contract address from blockchain config
  const rentalAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.rental_address
    : ("" as Address);

  /**
   * Check if insurance can be claimed for a rental
   */
  const checkInsuranceEligibility = async (
    rental: IRentalWithMetadata
  ): Promise<IInsuranceClaimEligibility> => {
    try {
      // Check if rental has expired
      if (!rental.expiresAt) {
        return {
          canClaim: false,
          reason: "Rental has no expiration date",
          recipient: "none",
        };
      }

      const expirationDate = new Date(rental.expiresAt);
      const now = new Date();

      if (now < expirationDate) {
        return {
          canClaim: false,
          reason: "Rental period has not expired yet",
          recipient: "none",
        };
      }

      // Check if insurance has already been claimed
      // Note: This would need to be checked via contract or API
      // For now, we'll assume it hasn't been claimed

      // Verify chicken death status
      const verification = await verifyChickenDeath(rental.chickenTokenId);

      if (!verification.isVerified) {
        return {
          canClaim: false,
          reason: "Chicken death status requires manual review",
          recipient: "none",
          verification,
        };
      }

      // Determine who can claim based on chicken status
      if (verification.isDead) {
        // Chicken died - owner gets insurance
        if (address?.toLowerCase() === rental.ownerAddress.toLowerCase()) {
          return {
            canClaim: true,
            reason: "Chicken died during rental - owner eligible for insurance",
            recipient: "owner",
            verification,
          };
        } else {
          return {
            canClaim: false,
            reason: "Only the owner can claim insurance for a dead chicken",
            recipient: "owner",
            verification,
          };
        }
      } else {
        // Chicken alive - renter gets insurance back
        if (address?.toLowerCase() === rental.renterAddress?.toLowerCase()) {
          return {
            canClaim: true,
            reason: "Chicken survived rental - renter eligible for insurance refund",
            recipient: "renter",
            verification,
          };
        } else {
          return {
            canClaim: false,
            reason: "Only the renter can claim insurance refund for a surviving chicken",
            recipient: "renter",
            verification,
          };
        }
      }
    } catch (error) {
      console.error("Insurance eligibility check failed:", error);
      return {
        canClaim: false,
        reason: `Eligibility check failed: ${error instanceof Error ? error.message : "Unknown error"}`,
        recipient: "none",
      };
    }
  };

  /**
   * Execute the insurance claiming process
   */
  const executeClaimInsurance = async (rental: IRentalWithMetadata) => {
    try {
      if (!address || !walletClient) {
        toast.error("Cannot claim insurance", {
          description: "Wallet not connected",
          position: "top-right",
        });
        Disconnect();
        return;
      }

      if (!rentalAddress) {
        toast.error("Cannot claim insurance", {
          description: "Rental contract not configured",
          position: "top-right",
        });
        return;
      }

      setIsClaiming(true);

      // Step 1: Check eligibility
      toast.info("Checking insurance eligibility...", {
        description: "Verifying chicken status and rental conditions",
        position: "top-center",
      });

      const eligibility = await checkInsuranceEligibility(rental);

      if (!eligibility.canClaim) {
        throw new Error(eligibility.reason);
      }

      // Step 2: Simulate the contract call
      toast.info("Simulating transaction...", {
        description: "Checking transaction validity",
        position: "top-center",
      });

      console.log("Claiming insurance for rental:", {
        rentId: BigInt(rental.id),
        recipient: eligibility.recipient,
        verification: eligibility.verification,
      });

      const simulateReq = await publicClient.simulateContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "claimInsurance",
        args: [BigInt(rental.id)],
        chain,
        account: address,
      });

      // Step 3: Estimate gas for the transaction
      const gasEstimate = await publicClient.estimateContractGas({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "claimInsurance",
        args: [BigInt(rental.id)],
        account: address,
      });

      // Step 4: Execute the transaction
      toast.info("Claiming insurance...", {
        description: "Please confirm the transaction in your wallet",
        position: "top-center",
      });

      const hash = await walletClient.writeContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "claimInsurance",
        args: [BigInt(rental.id)],
        gas: gasEstimate + BigInt(50000), // Add buffer
        chain,
        account: address,
      });

      // Step 5: Wait for transaction confirmation
      toast.info("Confirming transaction...", {
        description: "Waiting for blockchain confirmation",
        position: "top-center",
      });

      const receipt = await publicClient.waitForTransactionReceipt({
        hash,
        confirmations: 1,
      });

      if (receipt.status === "success") {
        const successMessage = eligibility.recipient === "owner" 
          ? "Insurance claimed successfully!" 
          : "Insurance refund claimed successfully!";
        
        const successDescription = eligibility.recipient === "owner"
          ? "You have received compensation for the lost chicken"
          : "Your insurance deposit has been refunded";

        toast.success(successMessage, {
          description: successDescription,
          position: "top-center",
        });

        // Invalidate relevant queries to refresh data
        setTimeout(() => {
          queryClient.invalidateQueries({ queryKey: ["my-rentals"] });
          queryClient.invalidateQueries({ queryKey: ["rental-history"] });
          queryClient.invalidateQueries({ queryKey: ["my-rentals", address] });
          queryClient.invalidateQueries({ 
            queryKey: ["chicken-death-verification", rental.chickenTokenId] 
          });
        }, 500);

        return {
          success: true,
          hash,
          receipt,
          eligibility,
        };
      } else {
        throw new Error("Transaction failed");
      }
    } catch (error) {
      console.error("Claim insurance failed:", error);

      let errorMessage = "Failed to claim insurance";
      let errorDescription = "An unexpected error occurred";

      if (error instanceof Error) {
        errorMessage = error.message;

        // Handle specific error cases
        if (error.message.includes("User rejected")) {
          errorDescription = "Transaction was cancelled by user";
        } else if (error.message.includes("insufficient funds")) {
          errorDescription = "Insufficient funds for gas fees";
        } else if (error.message.includes("already claimed")) {
          errorDescription = "Insurance has already been claimed";
        } else if (error.message.includes("not expired")) {
          errorDescription = "Rental period has not expired yet";
        } else if (error.message.includes("not eligible")) {
          errorDescription = "You are not eligible to claim this insurance";
        } else {
          errorDescription = error.message;
        }
      }

      toast.error(errorMessage, {
        description: errorDescription,
        position: "top-center",
      });

      return { success: false, error };
    } finally {
      setIsClaiming(false);
    }
  };

  // Mutation for React Query integration
  const claimInsuranceMutation = useMutation({
    mutationFn: executeClaimInsurance,
    onSuccess: (result) => {
      if (result?.success) {
        // Additional success handling if needed
      }
    },
    onError: (error) => {
      console.error("Claim insurance mutation error:", error);
    },
  });

  return {
    executeClaimInsurance,
    checkInsuranceEligibility,
    claimInsuranceMutation,
    isClaiming,
    rentalAddress,
  };
};
